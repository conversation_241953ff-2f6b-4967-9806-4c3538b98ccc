<template>
    <div class="timer-view">
        <TimerHeader 
            :searchQuery="searchQuery" 
            @search="handleSearch" 
            @createNew="createNewSession" 
        />
        <div class="timer-content">
            <section class="timer-section">
                <h2 class="section-title">Pomodoro Timer</h2>
                <PomodoroTimer 
                    ref="pomodoroTimerRef"
                    :sessionActive="timerStore.sessionActive" 
                    :sessionInfo="timerStore.activeSession"
                    @end-session="endSession" 
                    @session-completed="handleSessionCompleted"
                    @session-auto-created="handleAutoSessionCreated" 
                    @session-updated="handleSessionUpdated" 
                />
            </section>

            <section class="session-history-section">
                <div class="section-header">
                    <h2 class="section-title">Session History</h2>
                    <div class="session-controls" v-if="canScroll">
                        <div class="session-counter">
                            <span class="counter-text">
                                {{ actuallyVisibleRange.start + 1 }}-{{ actuallyVisibleRange.end }} of {{ totalSessions }}
                            </span>
                        </div>
                        <!-- Progress indicator in header -->
                        <div class="scroll-progress-bar">
                            <div class="progress-track"></div>
                            <div class="progress-thumb" :style="{ left: thumbPosition + '%' }"></div>
                        </div>
                        <div class="nav-controls">
                            <button
                                class="nav-control nav-control--prev"
                                @click="scrollToPrevious"
                                :disabled="!canScrollLeft"
                                :class="{ 'nav-control--disabled': !canScrollLeft }"
                            >
                                <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                                    <path d="M10 12L6 8L10 4" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                                </svg>
                            </button>
                            <button
                                class="nav-control nav-control--next"
                                @click="scrollToNext"
                                :disabled="!canScrollRight"
                                :class="{ 'nav-control--disabled': !canScrollRight }"
                            >
                                <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                                    <path d="M6 4L10 8L6 12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>

                <div class="sessions-viewport" ref="sessionsViewport">
                    <div 
                        class="sessions-container" 
                        ref="sessionsContainer"
                        @scroll="handleScroll"
                        @wheel="handleWheel"
                    >
                        <div class="sessions-track" :style="{ width: Math.max(totalWidth, viewportWidth) + 'px' }">
                            <!-- Active session -->
                            <div
                                v-if="displayActiveSession"
                                class="session-slot"
                                :style="getSlotStyle(0)"
                            >
                                <SessionCard
                                    :title="displayActiveSession.sessionName"
                                    :category="displayActiveSession.category"
                                    :date="displayActiveSession.date"
                                    :totalFocusTime="displayActiveSession.totalFocusTime"
                                    :pomodoroCount="displayActiveSession.pomodoroCount"
                                    :isActive="true"
                                    :sessionId="displayActiveSession.id"
                                    :focus="displayActiveSession.focus || ''"
                                    @view-details="handleViewSessionDetails"
                                />
                            </div>

                            <!-- Completed sessions -->
                            <div
                                v-for="session in visibleCompletedSessions"
                                :key="`session-${session.id}`"
                                class="session-slot"
                                :style="getSlotStyle(session.absoluteIndex)"
                            >
                                <SessionCard
                                    :title="session.sessionName"
                                    :category="session.category"
                                    :date="session.date"
                                    :totalFocusTime="session.totalFocusTime"
                                    :pomodoroCount="session.pomodoroCount"
                                    :isActive="false"
                                    :sessionId="session.id"
                                    :focus="session.focus || ''"
                                    @view-details="handleViewSessionDetails"
                                />
                            </div>
                        </div>
                    </div>

                    <!-- Empty state -->
                    <div v-if="totalSessions === 0" class="empty-sessions-state">
                        <div class="empty-sessions-text">No sessions yet</div>
                        <div class="empty-sessions-subtext">Start your first pomodoro session to see it here</div>
                    </div>
                </div>
            </section>

            <section class="stats-section">
                <h2 class="section-title">Stats</h2>
                <div class="stats-grid">
                    <StatCard title="Total Sessions" :value="stats.totalSessions" />
                    <StatCard title="Total Pomodoros" :value="stats.totalPomodoros" />
                    <StatCard title="Total Focus Time" :value="stats.totalFocusTime" />
                    <StatCard title="Sessions This Week" :value="stats.sessionsThisWeek" />
                </div>
            </section>

            <!-- Charts Section -->
            <ChartSection ref="chartSection" />
        </div>

        <!-- Modals -->
        <AddSessionModal 
            v-if="showAddSessionModal" 
            @close="showAddSessionModal = false"
            @start-session="startSession" 
        />
        <SessionDetailsModal 
            v-if="showSessionDetailsModal && selectedSession" 
            :sessionData="selectedSession"
            @close="showSessionDetailsModal = false" 
            @delete="handleDeleteSession" 
        />
    </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount, watch, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import TimerHeader from '../components/timer/TimerHeader.vue'
import PomodoroTimer from '../components/timer/PomodoroTimer.vue'
import SessionCard from '../components/timer/SessionCard.vue'
import StatCard from '../components/timer/StatCard.vue'
import ChartSection from '../components/timer/ChartSection.vue'
import AddSessionModal from '../components/modals/AddSessionModal.vue'
import SessionDetailsModal from '../components/modals/SessionDetailsModal.vue'
import { useElectronAPI } from '../useElectronAPI'
import { useTimerStore } from '../stores/timerStore'
import { useTimerKeybinds } from '../composables/useTimerKeybinds'

interface Session {
    id?: number
    sessionName: string
    focus?: string | null
    category: string
    date: Date
    endTime?: Date | null
    totalFocusTime: number
    pomodoroCount: number
    isActive: boolean
}

const db = useElectronAPI()
const timerStore = useTimerStore()
const route = useRoute()
const router = useRouter()

// Setup keybinds
const { setupTimerFunctions, activate: activateKeybinds, deactivate: deactivateKeybinds } = useTimerKeybinds()

// Core state
const searchQuery = ref('')
const showAddSessionModal = ref(false)
const showSessionDetailsModal = ref(false)
const selectedSession = ref<Session | null>(null)
const completedSessions = ref<Session[]>([])

// Chart section ref
const chartSection = ref<InstanceType<typeof ChartSection> | null>(null)

// Pomodoro timer ref for accessing timer controls
const pomodoroTimerRef = ref<InstanceType<typeof PomodoroTimer> | null>(null)

// Scroll system state
const sessionsViewport = ref<HTMLElement | null>(null)
const sessionsContainer = ref<HTMLElement | null>(null)
const isScrolling = ref(false)
const scrollPosition = ref(0)
const cardWidth = 300
const cardGap = 15
const cardTotal = cardWidth + cardGap

// Stats
const stats = ref({
    totalSessions: 0,
    totalPomodoros: 0,
    totalFocusTime: '0h 0m',
    sessionsThisWeek: 0
})

// Computed properties
const displayActiveSession = computed(() => {
    if (!timerStore.activeSession) return null

    const activeSession = {
        id: timerStore.activeSession.id,
        sessionName: timerStore.activeSession.session_name || 'Active Session',
        focus: timerStore.activeSession.focus,
        category: timerStore.activeSession.category || 'General',
        date: new Date(timerStore.activeSession.start_time),
        totalFocusTime: timerStore.totalFocusTime,
        pomodoroCount: timerStore.pomodoroCount,
        isActive: true
    }

    // If there's a search query, filter the active session too
    if (searchQuery.value.trim()) {
        const query = searchQuery.value.toLowerCase()
        const matchesSearch = activeSession.sessionName.toLowerCase().includes(query) ||
            (activeSession.focus && activeSession.focus.toLowerCase().includes(query)) ||
            activeSession.category.toLowerCase().includes(query)

        if (!matchesSearch) return null
    }

    return activeSession
})

const totalSessions = computed(() => {
    return (displayActiveSession.value ? 1 : 0) + filteredCompletedSessions.value.length
})

const totalWidth = computed(() => {
    if (totalSessions.value === 0) return 0
    // Calculate total content width: all cards with gaps, but no gap after the last card
    return totalSessions.value * cardWidth + Math.max(0, totalSessions.value - 1) * cardGap
})

const viewportWidth = computed(() => {
    return sessionsContainer.value?.clientWidth || 1000
})

const maxScroll = computed(() => {
    if (totalSessions.value === 0) return 0

    const contentWidth = totalWidth.value
    const viewport = viewportWidth.value

    // Maximum scroll is the difference between content width and viewport width
    // If content fits in viewport, max scroll should be 0
    return Math.max(0, contentWidth - viewport)
})

const canScroll = computed(() => {
    return totalWidth.value > viewportWidth.value && totalSessions.value > 0
})

const canScrollLeft = computed(() => {
    return canScroll.value && scrollPosition.value > 5
})

const canScrollRight = computed(() => {
    return canScroll.value && scrollPosition.value < (maxScroll.value - 5)
})

const scrollPercentage = computed(() => {
    if (!canScroll.value || maxScroll.value === 0) return 0
    return Math.min(100, Math.max(0, (scrollPosition.value / maxScroll.value) * 100))
})

// Computed property for progress thumb positioning that accounts for thumb width
const thumbPosition = computed(() => {
    if (!canScroll.value || maxScroll.value === 0) return 0
    const percentage = (scrollPosition.value / maxScroll.value) * 100
    // The thumb has a width of 20%, so we need to ensure it doesn't go beyond 80%
    // This way when percentage is 100%, the thumb will be at 80% and its right edge at 100%
    const thumbWidth = 20 // This should match the CSS width percentage
    const maxThumbPosition = 100 - thumbWidth
    return Math.min(maxThumbPosition, Math.max(0, percentage))
})

// Calculate the actually visible range (what user can see on screen)
const actuallyVisibleRange = computed(() => {
    if (totalSessions.value === 0) return { start: 0, end: 0 }

    const viewport = viewportWidth.value
    const scrollPos = scrollPosition.value

    // Calculate which sessions are actually visible in the viewport
    const firstVisibleIndex = Math.floor(scrollPos / cardTotal)
    const lastVisibleIndex = Math.min(
        Math.ceil((scrollPos + viewport) / cardTotal) - 1,
        totalSessions.value - 1
    )

    return {
        start: Math.max(0, firstVisibleIndex),
        end: Math.max(0, lastVisibleIndex + 1) // +1 because end is exclusive
    }
})

const visibleRange = computed(() => {
    if (totalSessions.value === 0) return { start: 0, end: 0 }

    // For small session counts, show all for optimal performance
    if (totalSessions.value <= 30) {
        return { start: 0, end: totalSessions.value }
    }

    // For larger counts, use seamless virtualization
    const startIndex = Math.floor(scrollPosition.value / cardTotal)
    const cardsInViewport = Math.ceil(viewportWidth.value / cardTotal)

    // Use generous buffer to ensure smooth scrolling without gaps
    const bufferCards = Math.max(8, Math.ceil(cardsInViewport * 0.75))
    const cardsToShow = cardsInViewport + bufferCards * 2

    // Calculate start position with proper boundary checking
    const idealStart = Math.max(0, startIndex - bufferCards)
    const actualStart = Math.min(idealStart, Math.max(0, totalSessions.value - cardsToShow))

    // Ensure we don't exceed total sessions
    const end = Math.min(actualStart + cardsToShow, totalSessions.value)

    return { start: actualStart, end: end }
})

// First filter sessions based on search query
const filteredCompletedSessions = computed(() => {
    if (completedSessions.value.length === 0) return []

    // If no search query, return all sessions
    if (!searchQuery.value.trim()) {
        return completedSessions.value
    }

    // Filter sessions based on search query
    const query = searchQuery.value.toLowerCase()
    return completedSessions.value.filter(session =>
        session.sessionName.toLowerCase().includes(query) ||
        (session.focus && session.focus.toLowerCase().includes(query)) ||
        session.category.toLowerCase().includes(query)
    )
})

const visibleCompletedSessions = computed(() => {
    if (filteredCompletedSessions.value.length === 0) return []

    const activeOffset = displayActiveSession.value ? 1 : 0
    const start = Math.max(0, visibleRange.value.start - activeOffset)
    const end = Math.max(0, visibleRange.value.end - activeOffset)

    return filteredCompletedSessions.value.slice(start, end).map((session, index) => ({
        ...session,
        absoluteIndex: start + index + activeOffset
    }))
})

// Utility functions
const getSlotStyle = (absoluteIndex: number) => {
    return {
        transform: `translateX(${absoluteIndex * cardTotal}px)`,
        width: `${cardWidth}px`
    }
}

const clampScrollPosition = (position: number) => {
    const maxScrollValue = maxScroll.value

    // Ensure position is within valid bounds
    if (maxScrollValue <= 0) return 0

    // Clamp between 0 and maxScroll with a small tolerance for floating point precision
    const clampedPosition = Math.max(0, Math.min(position, maxScrollValue))

    // Round to avoid floating point precision issues
    return Math.round(clampedPosition * 100) / 100
}

const smoothScrollTo = (targetPosition: number) => {
    if (!sessionsContainer.value) return Promise.resolve()
    
    const clampedTarget = clampScrollPosition(targetPosition)
    const startPosition = scrollPosition.value
    const distance = clampedTarget - startPosition
    
    if (Math.abs(distance) < 1) return Promise.resolve()
    
    return new Promise((resolve) => {
        isScrolling.value = true
        const duration = 300
        const startTime = performance.now()
        
        const animate = (currentTime: number) => {
            const elapsed = currentTime - startTime
            const progress = Math.min(elapsed / duration, 1)
            const easedProgress = 1 - Math.pow(1 - progress, 3) // easeOutCubic
            
            const currentPosition = startPosition + distance * easedProgress
            const finalPosition = clampScrollPosition(currentPosition)
            
            sessionsContainer.value!.scrollLeft = finalPosition
            scrollPosition.value = finalPosition
            
            if (progress < 1) {
                requestAnimationFrame(animate)
            } else {
                isScrolling.value = false
                resolve(undefined)
            }
        }
        
        requestAnimationFrame(animate)
    })
}

const scrollToPrevious = async () => {
    if (!canScrollLeft.value || isScrolling.value) return
    
    const cardsPerView = Math.max(1, Math.floor(viewportWidth.value / cardTotal))
    const scrollAmount = cardsPerView * cardTotal
    const targetPosition = Math.max(0, scrollPosition.value - scrollAmount)
    
    await smoothScrollTo(targetPosition)
}

const scrollToNext = async () => {
    if (!canScrollRight.value || isScrolling.value) return
    
    const cardsPerView = Math.max(1, Math.floor(viewportWidth.value / cardTotal))
    const scrollAmount = cardsPerView * cardTotal
    const targetPosition = scrollPosition.value + scrollAmount
    
    await smoothScrollTo(targetPosition)
}

const scrollToStart = async () => {
    await smoothScrollTo(0)
}

const handleScroll = () => {
    if (!sessionsContainer.value) return

    const containerScrollLeft = sessionsContainer.value.scrollLeft
    const newPosition = clampScrollPosition(containerScrollLeft)

    // Update our tracked scroll position
    if (!isScrolling.value || Math.abs(newPosition - scrollPosition.value) > 2) {
        scrollPosition.value = newPosition
    }

    // Enforce scroll boundaries by correcting the container's scroll position if needed
    if (Math.abs(containerScrollLeft - newPosition) > 1) {
        // Use requestAnimationFrame to avoid infinite scroll event loops
        requestAnimationFrame(() => {
            if (sessionsContainer.value) {
                sessionsContainer.value.scrollLeft = newPosition
            }
        })
    }
}

const handleWheel = (event: WheelEvent) => {
    if (!canScroll.value || totalSessions.value === 0) return

    event.preventDefault()

    if (isScrolling.value) {
        isScrolling.value = false
    }

    const delta = event.deltaY || event.deltaX
    const scrollSpeed = 2
    const targetPosition = scrollPosition.value + delta * scrollSpeed
    const newPosition = clampScrollPosition(targetPosition)

    // Only update if there's a meaningful change and we're within bounds
    if (sessionsContainer.value && Math.abs(newPosition - scrollPosition.value) > 0.5) {
        // Ensure we don't exceed boundaries
        const maxScrollValue = maxScroll.value
        if (newPosition >= 0 && newPosition <= maxScrollValue) {
            sessionsContainer.value.scrollLeft = newPosition
            scrollPosition.value = newPosition
        }
    }
}

// Data loading
const loadSessions = async () => {
    try {
        const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
        const today = new Date().toISOString().split('T')[0]
        const sessions = await db.timer.getSessionsByDateRange(thirtyDaysAgo, today)

        completedSessions.value = sessions
            .filter(session => session.is_completed === 1 && session.is_user_session === 1)
            .map(session => ({
                id: session.id,
                sessionName: session.session_name || session.focus || 'Unnamed Session',
                focus: session.focus,
                category: session.category || 'No Category',
                date: new Date(session.start_time),
                endTime: session.end_time ? new Date(session.end_time) : null,
                totalFocusTime: session.duration || 0,
                pomodoroCount: session.pomodoro_cycles_completed || 0,
                isActive: false
            }))
    } catch (error) {
        console.error('Failed to load sessions:', error)
    }
}

const loadStats = async () => {
    try {
        const today = new Date().toISOString().split('T')[0]
        const weekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]

        const weekStats = await db.timer.getStatsByDateRange(weekAgo, today)
        const allTimeStats = await db.timer.getStatsByDateRange('2020-01-01', today)

        stats.value = {
            totalSessions: allTimeStats.total_sessions,
            totalPomodoros: allTimeStats.total_pomodoros || 0,
            totalFocusTime: formatDuration(allTimeStats.work_duration || 0),
            sessionsThisWeek: weekStats.total_sessions
        }
    } catch (error) {
        console.error('Failed to load statistics:', error)
    }
}

const formatDuration = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    return `${hours}h ${minutes}m`
}

// Event handlers
const handleSearch = async (query: string) => {
    searchQuery.value = query
    // Reset scroll position when searching to show filtered results from the beginning
    await nextTick()
    scrollToStart()
}

const createNewSession = () => {
    showAddSessionModal.value = true
}

const startSession = async (sessionData: { sessionName: string; focus?: string; category: string }) => {
    try {
        await timerStore.createSession(sessionData.sessionName, sessionData.focus, sessionData.category)
        showAddSessionModal.value = false
        await nextTick()
        scrollToStart()
    } catch (error) {
        console.error('Failed to start session:', error)
    }
}

const endSession = async () => {
    try {
        const endedSession = await timerStore.endSession()
        if (endedSession) {
            const completedSession = {
                id: endedSession.id,
                sessionName: endedSession.session_name || 'Completed Session',
                focus: endedSession.focus,
                category: endedSession.category || 'General',
                date: new Date(endedSession.start_time),
                endTime: endedSession.end_time ? new Date(endedSession.end_time) : null,
                totalFocusTime: endedSession.duration || 0,
                pomodoroCount: endedSession.pomodoro_cycles_completed || 0,
                isActive: false
            }
            completedSessions.value.unshift(completedSession)
        }
        await loadStats()
        await loadSessions()
        // Refresh charts with new data
        chartSection.value?.refreshCharts()
        await nextTick()
        scrollToStart()
    } catch (error) {
        console.error('Failed to end session:', error)
    }
}

const handleSessionCompleted = async () => {
    await loadStats()
    // Refresh charts when session is completed
    chartSection.value?.refreshCharts()
}

const handleSessionUpdated = (sessionData: Partial<Session>) => {
    if (!timerStore.activeSession) return
    Object.assign(timerStore.activeSession, sessionData)
}

const handleAutoSessionCreated = () => {
    nextTick().then(() => {
        scrollToStart()
    })
}

const handleViewSessionDetails = (sessionData: any) => {
    selectedSession.value = {
        ...sessionData,
        isActive: sessionData.isActive ?? false
        // endTime is already included in sessionData from loadSessions
    }
    showSessionDetailsModal.value = true
}

const handleDeleteSession = async (sessionId: number) => {
    try {
        // If this is the active session, end it first before deleting
        if (timerStore.activeSession?.id === sessionId) {
            await timerStore.endSession()
        }

        // Now delete the session from the database
        const result = await db.timer.deleteSession(sessionId)
        if (result.success) {
            completedSessions.value = completedSessions.value.filter(
                session => session.id !== sessionId
            )
            showSessionDetailsModal.value = false
            selectedSession.value = null
            await loadStats()
            // Refresh charts after session deletion
            chartSection.value?.refreshCharts()
        }
    } catch (error) {
        console.error('Error deleting session:', error)
    }
}

// Lifecycle
let resizeObserver: ResizeObserver | null = null

// Handle action from dashboard
const handleStartAction = async () => {
    try {
        // Clean URL first
        await router.replace({ query: { ...route.query, action: undefined } })
        
        // Just start the timer - it will create a generic session automatically if needed
        if (pomodoroTimerRef.value) {
            (pomodoroTimerRef.value as any).handleToggleTimer()
        }
    } catch (error) {
        console.error('Failed to start timer from dashboard action:', error)
    }
}

// Configure keybind functions
setupTimerFunctions({
    toggleTimer: () => {
        // Access the timer toggle method through the ref
        if (pomodoroTimerRef.value) {
            (pomodoroTimerRef.value as any).handleToggleTimer()
        }
    },
    resetTimer: () => {
        // Access the timer reset method through the ref
        if (pomodoroTimerRef.value) {
            (pomodoroTimerRef.value as any).handleRestartTimer()
        }
    },
    skipTimer: () => {
        // Access the timer skip method through the ref
        if (pomodoroTimerRef.value) {
            (pomodoroTimerRef.value as any).handleSkipTimer()
        }
    },
    switchToPomodoro: () => {
        timerStore.switchTimerType('pomodoro')
    },
    switchToShortBreak: () => {
        timerStore.switchTimerType('shortBreak')
    },
    switchToLongBreak: () => {
        timerStore.switchTimerType('longBreak')
    },
    createNewSession: () => {
        showAddSessionModal.value = true
    },
    endCurrentSession: () => {
        if (pomodoroTimerRef.value) {
            (pomodoroTimerRef.value as any).handleEndSession()
        }
    },
    openTimerSettings: () => {
        // We could emit an event to open timer settings modal
        // For now, let's skip this as it requires more complex integration
        console.log('Timer settings shortcut pressed')
    }
})

onMounted(async () => {
    // Load data in parallel but don't wait for it if we need to start timer
    const loadPromise = Promise.all([loadSessions(), loadStats()])
    
    // Handle action parameter immediately
    const actionParam = route.query.action
    if (actionParam === 'start') {
        // Wait a tick for the component to be fully mounted
        await nextTick()
        await handleStartAction()
    } else {
        // Only wait for loading if we're not auto-starting
        await loadPromise
    }

    // Activate keybinds for this view
    activateKeybinds()

    resizeObserver = new ResizeObserver(() => {
        if (sessionsContainer.value) {
            // Recalculate scroll boundaries after resize
            nextTick(() => {
                const clampedPosition = clampScrollPosition(scrollPosition.value)
                if (Math.abs(clampedPosition - scrollPosition.value) > 1) {
                    scrollPosition.value = clampedPosition
                    sessionsContainer.value!.scrollLeft = clampedPosition
                }
                handleScroll()
            })
        }
    })

    if (sessionsViewport.value) {
        resizeObserver.observe(sessionsViewport.value)
    }
})

onBeforeUnmount(() => {
    // Deactivate keybinds
    deactivateKeybinds()
    
    if (resizeObserver) {
        resizeObserver.disconnect()
    }
})

watch([totalSessions, viewportWidth], () => {
    nextTick(() => {
        // When session count or viewport changes, ensure scroll position is valid
        const clampedPosition = clampScrollPosition(scrollPosition.value)
        if (Math.abs(clampedPosition - scrollPosition.value) > 1) {
            scrollPosition.value = clampedPosition
            if (sessionsContainer.value) {
                sessionsContainer.value.scrollLeft = clampedPosition
            }
        }
    })
})

// Watch for action parameter changes
watch(() => route.query.action, async (action) => {
    if (action === 'start') {
        await handleStartAction()
    }
})
</script>

<style scoped>
.timer-view {
    display: flex;
    flex-direction: column;
    height: 100%;
    width: 100%;
    font-family: 'Montserrat', sans-serif;
}

.timer-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 20px;
    overflow-y: auto;
    gap: 15px;
}

.timer-content::-webkit-scrollbar {
    width: 8px;
}

.timer-content::-webkit-scrollbar-track {
    background: var(--color-scrollbar-track);
    border-radius: 0 8px 8px 0;
}

.timer-content::-webkit-scrollbar-thumb {
    background: var(--color-scrollbar-thumb);
    border-radius: 4px;
}

.timer-content::-webkit-scrollbar-thumb:hover {
    background: var(--color-scrollbar-thumb-hover);
}

.section-title {
    color: var(--color-text-primary);
    font-size: 18px;
    font-weight: 700;
    margin-bottom: 20px;
    margin-left: 10px;
}

.section-header {
    display: grid;
    grid-template-columns: 1fr auto 1fr;
    align-items: center;
    margin-bottom: 0;
    width: 100%;
    padding-left: 10px; /* NEW: Match the section-title margin-left */
    padding-right: 10px; /* NEW: Balance the spacing on both sides */
}

.section-header .section-title {
    grid-column: 1;
    justify-self: start;
    margin-bottom: 0;
    margin-left: 0; /* Reset since parent has padding now */
}

.section-header .session-controls {
    grid-column: 3;
    justify-self: end;
    display: flex;
    align-items: center;
    gap: 15px;
}

.session-controls {
    display: flex;
    align-items: center;
    gap: 15px;
}

.counter-text {
    font-size: 12px;
    color: var(--color-text-tertiary);
    font-weight: 500;
    min-width: 80px;
    text-align: right;
}

.nav-controls {
    display: flex;
    gap: 5px;
}

.nav-control {
    width: 32px;
    height: 32px;
    background: var(--color-btn-secondary-bg);
    border: 1px solid var(--color-btn-secondary-border);
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: var(--color-text-secondary);
    transition: all 0.2s ease;
}

.nav-control:hover:not(.nav-control--disabled) {
    background: var(--color-btn-secondary-hover);
    color: var(--color-text-primary);
    border-color: var(--color-border-hover);
}

.nav-control:active:not(.nav-control--disabled) {
    transform: scale(0.95);
}

.nav-control--disabled {
    opacity: 0.4;
    cursor: not-allowed;
}

.sessions-viewport {
    position: relative;
    width: 100%;
    height: 127px;
    margin-top: 20px;
    overflow: hidden;
}

.sessions-container {
    width: 100%;
    height: 100%;
    overflow-x: auto;
    overflow-y: hidden;
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.sessions-container::-webkit-scrollbar {
    display: none;
}

.sessions-track {
    position: relative;
    height: 100%;
    min-width: 100%;
}

.session-slot {
    position: absolute;
    top: 0;
    height: 100%;
    width: 300px;
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.scroll-progress-bar {
    position: relative;
    width: 120px;
    height: 4px;
    background: var(--color-border-secondary);
    border-radius: 2px;
    overflow: hidden;
    align-self: center;
}

.progress-track {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--color-border-primary);
}

.progress-thumb {
    position: absolute;
    top: 0;
    bottom: 0;
    width: 20%;
    background: var(--color-primary);
    border-radius: 2px;
    transition: left 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.empty-sessions-state {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    color: var(--color-text-tertiary);
}

.empty-sessions-text {
    font-size: 18px;
    font-weight: 500;
    margin-bottom: 8px;
    color: var(--color-text-secondary);
}

.empty-sessions-subtext {
    font-size: 14px;
    color: var(--color-text-tertiary);
    max-width: 300px;
    line-height: 1.4;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 15px;
    width: 100%;
}

@media (max-width: 768px) {
    .timer-content {
        padding: 16px;
    }
    
    .section-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
    }
    
    .session-controls {
        align-self: stretch;
        justify-content: space-between;
    }
    
    .sessions-viewport {
        height: 120px;
    }
    
    .nav-control {
        width: 28px;
        height: 28px;
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 12px;
    }
}
</style>