# Dashboard Quick Actions Implementation Plan

## Overview
Fix dashboard buttons to perform their intended actions instead of just navigating to views. Use query parameters to communicate action intent between dashboard and target views.

## Current Problem
Dashboard buttons in `QuickActions.vue` only navigate to views:
- **New Note** → `router.push('/notes')` (should create note)
- **Add Book** → `router.push('/books')` (should open AddBookModal)
- **Start Timer** → `router.push('/timer')` (should auto-start timer)
- **New Folder** → `router.push('/folders')` (should open NameFolderModal)

## Solution: Query Parameter Actions

### Why Query Parameters?
1. **Stateless** - URL contains all needed information
2. **Bookmarkable** - Users can bookmark action URLs
3. **Testable** - Easy to test by navigating to URLs
4. **Clear Intent** - URL shows what should happen
5. **Backward Compatible** - Views work normally without parameters

## Implementation Strategy

### Phase 1: Update Dashboard Navigation
**File:** `src/components/dashboard/QuickActions.vue`

```typescript
const createNewNote = () => {
  router.push('/notes?action=create')
}

const addNewBook = () => {
  router.push('/books?action=add')
}

const startTimer = () => {
  router.push('/timer?action=start')
}

const createNewFolder = () => {
  router.push('/folders?action=create')
}
```

### Phase 2: Update Target Views

#### NotesView (`src/views/NotesView.vue`)
**Current Pattern:** Already handles `noteId` query parameter with watcher
**New Addition:** Handle `action=create` parameter

```typescript
// Add to existing route watcher or create new one
watch(() => route.query.action, async (action) => {
  if (action === 'create' && !loading.value) {
    await handleCreateAction()
  }
})

const handleCreateAction = async () => {
  try {
    // Clean URL first
    await router.replace({ query: { ...route.query, action: undefined } })
    
    // Create note after data is loaded
    if (loading.value) {
      // Wait for data to load
      await new Promise(resolve => {
        const unwatch = watch(() => loading.value, (isLoading) => {
          if (!isLoading) {
            unwatch()
            resolve(void 0)
          }
        })
      })
    }
    
    await createNewNote()
  } catch (error) {
    console.error('Failed to create note from dashboard action:', error)
  }
}

// Add to onMounted after loadNotes()
onMounted(() => {
  loadNotes().then(() => {
    // Existing noteId handling...
    
    // Handle action parameter
    const actionParam = route.query.action
    if (actionParam === 'create') {
      handleCreateAction()
    }
  })
})
```

#### BooksView (`src/views/BooksView.vue`)
**Current Pattern:** Modal state managed with `showAddBookModal.value`
**New Addition:** Handle `action=add` parameter

```typescript
// Add route watcher for action
watch(() => route.query.action, async (action) => {
  if (action === 'add' && !loading.value) {
    await handleAddAction()
  }
})

const handleAddAction = async () => {
  try {
    // Clean URL first
    await router.replace({ query: { ...route.query, action: undefined } })
    
    // Wait for data loading if needed
    if (loading.value) {
      await new Promise(resolve => {
        const unwatch = watch(() => loading.value, (isLoading) => {
          if (!isLoading) {
            unwatch()
            resolve(void 0)
          }
        })
      })
    }
    
    // Open modal
    showAddBookModal.value = true
  } catch (error) {
    console.error('Failed to open add book modal from dashboard action:', error)
  }
}

// Add to onMounted after loadBooks()
onMounted(() => {
  loadBooks().then(() => {
    // Handle action parameter
    const actionParam = route.query.action
    if (actionParam === 'add') {
      handleAddAction()
    }
  })
})
```

#### TimerView (`src/views/TimerView.vue`)
**Current Pattern:** Session modal managed with `showAddSessionModal.value`
**New Addition:** Handle `action=start` parameter

```typescript
// Add route watcher for action
watch(() => route.query.action, async (action) => {
  if (action === 'start') {
    await handleStartAction()
  }
})

const handleStartAction = async () => {
  try {
    // Clean URL first
    await router.replace({ query: { ...route.query, action: undefined } })
    
    // Wait for data loading
    await Promise.all([loadSessions(), loadStats()])
    
    // Auto-start timer or show session modal
    if (timerStore.activeSession) {
      // Resume existing session
      if (pomodoroTimerRef.value) {
        (pomodoroTimerRef.value as any).handleToggleTimer()
      }
    } else {
      // Show session creation modal
      showAddSessionModal.value = true
    }
  } catch (error) {
    console.error('Failed to start timer from dashboard action:', error)
  }
}

// Add to onMounted after existing loading
onMounted(async () => {
  await loadSessions()
  await loadStats()
  
  // Handle action parameter
  const actionParam = route.query.action
  if (actionParam === 'start') {
    await handleStartAction()
  }
  
  // Existing code...
})
```

#### FoldersView (`src/views/FoldersView.vue`)
**Current Pattern:** Modal state managed with `showNameFolderModal.value`
**New Addition:** Handle `action=create` parameter

```typescript
// Add route watcher for action
watch(() => route.query.action, async (action) => {
  if (action === 'create' && !loading.value) {
    await handleCreateAction()
  }
})

const handleCreateAction = async () => {
  try {
    // Clean URL first
    await router.replace({ query: { ...route.query, action: undefined } })
    
    // Wait for data loading if needed
    if (loading.value) {
      await new Promise(resolve => {
        const unwatch = watch(() => loading.value, (isLoading) => {
          if (!isLoading) {
            unwatch()
            resolve(void 0)
          }
        })
      })
    }
    
    // Create folder at root level
    await createNewFolder(null)
  } catch (error) {
    console.error('Failed to create folder from dashboard action:', error)
  }
}

// Add to onMounted after existing initialization
onMounted(async () => {
  // Existing initialization...
  
  // Handle action parameter
  const actionParam = route.query.action
  if (actionParam === 'create') {
    await handleCreateAction()
  }
})
```

## Key Implementation Details

### Timing Considerations
1. **Data Loading First** - Always wait for view data to load before executing actions
2. **URL Cleanup** - Use `router.replace()` to remove action parameter immediately
3. **Error Handling** - Graceful fallback if actions fail
4. **Single Execution** - Actions only trigger once per navigation

### URL Cleanup Pattern
```typescript
// Clean URL immediately to prevent re-triggering
await router.replace({ 
  query: { 
    ...route.query, 
    action: undefined 
  } 
})
```

### Loading State Handling
```typescript
// Wait for loading to complete if needed
if (loading.value) {
  await new Promise(resolve => {
    const unwatch = watch(() => loading.value, (isLoading) => {
      if (!isLoading) {
        unwatch()
        resolve(void 0)
      }
    })
  })
}
```

## Testing Strategy
1. **Manual Testing** - Navigate to each URL with action parameter
2. **Edge Cases** - Test with existing query parameters
3. **Timing** - Test rapid navigation scenarios
4. **Error Cases** - Test when data loading fails

## Benefits
- **Immediate User Feedback** - Actions happen right after navigation
- **Consistent UX** - Same behavior whether from dashboard or direct URL
- **Maintainable** - Each view handles its own action logic
- **Extensible** - Easy to add more action types in future

## Files to Modify
1. `src/components/dashboard/QuickActions.vue` - Update navigation calls
2. `src/views/NotesView.vue` - Add action=create handling
3. `src/views/BooksView.vue` - Add action=add handling  
4. `src/views/TimerView.vue` - Add action=start handling
5. `src/views/FoldersView.vue` - Add action=create handling

## Edge Cases & Considerations

### Multiple Query Parameters
Handle cases where action parameter coexists with other parameters:
```typescript
// Preserve existing parameters while cleaning action
const { action, ...otherParams } = route.query
await router.replace({ query: otherParams })
```

### Rapid Navigation
Prevent action re-triggering during rapid navigation:
```typescript
const actionProcessed = ref(false)

watch(() => route.query.action, async (action) => {
  if (action && !actionProcessed.value) {
    actionProcessed.value = true
    await handleAction()
  }
})

// Reset on route change
watch(() => route.path, () => {
  actionProcessed.value = false
})
```

### Component Unmounting
Handle cases where user navigates away during action processing:
```typescript
const isMounted = ref(true)

onBeforeUnmount(() => {
  isMounted.value = false
})

const handleAction = async () => {
  // Check if still mounted before proceeding
  if (!isMounted.value) return

  // ... action logic
}
```

### Error Recovery
Provide fallback behavior when actions fail:
```typescript
const handleCreateAction = async () => {
  try {
    await createNewNote()
  } catch (error) {
    console.error('Dashboard action failed:', error)
    // Fallback: just ensure view is in good state
    if (notes.value.length > 0 && !selectedNote.value) {
      selectNote(notes.value[0])
    }
  }
}
```

### Modal Focus Management
Ensure modals receive proper focus when opened via actions:
```typescript
const handleAddAction = async () => {
  showAddBookModal.value = true

  // Ensure modal gets focus after rendering
  await nextTick()
  // Modal components should handle their own focus in onMounted
}
```

## Performance Considerations

### Debounced Action Processing
Prevent multiple rapid action triggers:
```typescript
import { debounce } from 'lodash-es'

const debouncedHandleAction = debounce(async (action: string) => {
  if (action === 'create') {
    await handleCreateAction()
  }
}, 100)

watch(() => route.query.action, debouncedHandleAction)
```

### Lazy Loading Compatibility
Ensure actions work with component lazy loading:
```typescript
// In router/index.ts - no changes needed
// Views are already lazy loaded, actions will work after component mounts
```

## Browser Compatibility
- **Hash Router** - Already using `createWebHashHistory()` for Electron
- **Query Parameters** - Standard URL API, works in all modern browsers
- **Router.replace()** - Vue Router method, fully supported

## Security Considerations
- **Input Validation** - Action parameters are predefined strings, no user input
- **XSS Prevention** - No dynamic HTML generation from parameters
- **URL Manipulation** - Actions are safe, only trigger internal functions

## Implementation Order
1. **Phase 1:** Update QuickActions.vue navigation (5 min)
2. **Phase 2:** Implement NotesView action handling (15 min)
3. **Phase 3:** Implement BooksView modal triggering (15 min)
4. **Phase 4:** Implement FoldersView modal triggering (15 min)
5. **Phase 5:** Implement TimerView auto-start logic (20 min)
6. **Phase 6:** Add edge case handling (15 min)
7. **Phase 7:** Test all scenarios thoroughly (30 min)

**Total Estimated Time:** ~2 hours

## Testing Scenarios

### Manual Test Cases
1. **Basic Actions**
   - Navigate to `/#/notes?action=create` → Should create new note
   - Navigate to `/#/books?action=add` → Should open AddBookModal
   - Navigate to `/#/timer?action=start` → Should show session modal or start timer
   - Navigate to `/#/folders?action=create` → Should open NameFolderModal

2. **Combined Parameters**
   - Navigate to `/#/notes?noteId=123&action=create` → Should open note 123, then create new note
   - Navigate to `/#/books?bookId=456&action=add` → Should handle both parameters gracefully

3. **Rapid Navigation**
   - Click dashboard button multiple times quickly → Should only trigger action once
   - Navigate between views rapidly → Should not cause errors

4. **Error Scenarios**
   - Navigate to action URL when database is unavailable → Should handle gracefully
   - Navigate to action URL when view is still loading → Should wait for loading

### Automated Testing Approach
```typescript
// Example test structure (for future implementation)
describe('Dashboard Quick Actions', () => {
  it('should create note when navigating with action=create', async () => {
    await router.push('/notes?action=create')
    await nextTick()
    expect(mockCreateNote).toHaveBeenCalled()
  })

  it('should clean URL after processing action', async () => {
    await router.push('/notes?action=create')
    await nextTick()
    expect(router.currentRoute.value.query.action).toBeUndefined()
  })
})
```

## Success Criteria
- [ ] New Note button creates note and navigates to it
- [ ] Add Book button opens AddBookModal immediately
- [ ] Start Timer button shows session modal or starts existing session
- [ ] New Folder button opens NameFolderModal at root level
- [ ] URLs are cleaned after action processing
- [ ] Actions work with existing query parameters
- [ ] No duplicate action triggering on rapid navigation
- [ ] Graceful error handling for all failure cases
- [ ] Modal focus management works correctly
- [ ] Performance remains smooth during rapid navigation

## Future Enhancements
- Add more action types (e.g., `action=edit&noteId=123`)
- Support action chaining (e.g., `action=create&then=edit`)
- Add analytics tracking for dashboard action usage
- Implement action history for debugging

## Documentation Updates Needed
- Update user documentation about bookmarkable action URLs
- Add developer documentation about adding new action types
- Update API documentation if any new interfaces are added
